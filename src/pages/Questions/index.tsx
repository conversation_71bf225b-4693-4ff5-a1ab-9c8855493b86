import { type ColumnDef } from "@tanstack/react-table";
import { CustomTable } from "@/components/CustomTable";
import { Button } from "@/components/ui/button";
import { Edit, Trash } from "lucide-react";
import { CustomModal } from "@/components/CustomModal";
import { useState } from "react";
import type { Question } from "@/types/question";


export const PaymentTable = () => {
  const [isOpenCreate, setIsOpenCreate] = useState(false);
  const [isOpenEdit, setIsOpenEdit] = useState(false);

  const data: Question[] = [
    {
      id: "m5gr84i9",
      question_en: "What is your name?",
      question_vi: "Tên bạn là gì?",
      example_en: "My name is <PERSON>.",
      example_vi: "Tên tôi là John.",
      question_variant: [
        {
          id: "1",
          name: "A",
          options: [
            {
              id: "1",
              text_en: "<PERSON>",
              text_vi: "<PERSON>",
            },
            {
              id: "2",
              text_en: "<PERSON>",
              text_vi: "<PERSON>",
            },
            {
              id: "3",
              text_en: "Jack",
              text_vi: "Jack",
            },
          ],
        },
      ],
    },
    // ... other data
  ];

  const columns: ColumnDef<Question>[] = [
    {
      accessorKey: "question_variant[0].name",
      header: "Loại câu hỏi",
      cell: ({ row }) => (
        <div className="capitalize text-start">{row.getValue("question_variant[0].name")}</div>
      ),
    },
    {
      accessorKey: "question_en",
      header: "Câu hỏi tiếng Anh",
      cell: ({ row }) => (
        <div className="capitalize text-start">{row.getValue("question_en")}</div>
      ),
      meta: {
        className: "w-[200px]", // Sử dụng Tailwind CSS
      },
    },
    {
      accessorKey: "question_vi",
      header: "Câu hỏi tiếng Việt",
      cell: ({ row }) => (
        <div className="lowercase text-start">{row.getValue("question_vi")}</div>
      ),
      size: 300,
    },
    {
      accessorKey: "example_en",
      header: "Ví dụ (Tiếng Anh)",
      cell: ({ row }) => (
        <div className="lowercase text-start">{row.getValue("example_en")}</div>
      ),
    },
    {
      accessorKey: "example_vi",
      header: "Ví dụ (Tiếng Việt)",
      cell: ({ row }) => (
        <div className="lowercase text-start">{row.getValue("example_vi")}</div>
      ),
    },
    {
      accessorKey: "",
      header: "Hành động",
      cell: () => (
        <div className="flex gap-2 text-start">
          <Button onClick={() => setIsOpenEdit(true)}>
            <Edit />
          </Button>
          <Button variant="destructive">
            <Trash />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <>
      <Button onClick={() => setIsOpenCreate(true)}>Open Modal</Button>
      <CustomTable<Question>
        data={data}
        columns={columns}
        title="Payments"
        description="List of all payments processed"
        searchPlaceholder="Filter payments..."
        searchColumn="email"
      />
      <CustomModal
        open={isOpenCreate}
        onOpenChange={setIsOpenCreate}
        modalTitle="Thêm mới câu hỏi"
        modalDescription="Modal Description"
      >
        Thêm mới
      </CustomModal>
      <CustomModal
        open={isOpenEdit}
        onOpenChange={setIsOpenEdit}
        modalTitle="Sửa câu hỏi"
        modalDescription="Modal Description"
      >
        Sửa
      </CustomModal>
    </>
  );
};
